package com.augmentcode.intellij.api

import com.augmentcode.api.ChatRequest
import com.augmentcode.api.NumericEnum
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.google.gson.FieldNamingPolicy
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonElement
import com.google.gson.JsonPrimitive
import com.google.gson.JsonSerializationContext
import com.google.gson.JsonSerializer
import com.google.protobuf.util.JsonFormat
import io.ktor.client.engine.mock.*
import io.ktor.client.engine.mock.MockEngine
import io.ktor.http.*
import io.ktor.utils.io.*
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.test.runTest
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.FindMissingRequest
import java.lang.reflect.Type

@RunWith(JUnit4::class)
class AugmentApiTest : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.modelName = null
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  @Test
  fun testFindMissing() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          assertEquals("POST", request.method.value)
          assertEquals("/find-missing", request.url.encodedPath)

          val requestBody = request.body.toByteArray().decodeToString()
          val findRequest =
            FindMissingRequest.newBuilder().apply {
              JsonFormat.parser().merge(requestBody, this)
            }.build()
          assertEquals("test-model", findRequest.model)

          respond(
            content =
              """
              {
                "unknown_memory_names": ["blob1"],
                "nonindexed_blob_names": ["blob2"]
              }
              """.trimIndent(),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      val request =
        FindMissingRequest.newBuilder().apply {
          model = "test-model"
        }.build()
      val response = api.findMissing(request)

      assertEquals(setOf("blob1"), response.unknownMemoryNamesList.toSet())
      assertEquals(setOf("blob2"), response.nonindexedBlobNamesList.toSet())
    }

  @Test
  fun testFindMissingNonSuccessful() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          assertEquals("POST", request.method.value)
          assertEquals("/find-missing", request.url.encodedPath)

          respond(
            content =
              """
              {
                "error": "Something went wrong"
              }
              """.trimIndent(),
            status = HttpStatusCode.InternalServerError,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      val request =
        FindMissingRequest.newBuilder().apply {
          model = "test-model"
        }.build()
      val response = api.findMissing(request)

      assertTrue(response.unknownMemoryNamesList.isEmpty())
      assertTrue(response.nonindexedBlobNamesList.isEmpty())
    }

  @Test
  fun testNetworkErrorIllegalState() =
    runTest {
      val mockEngine =
        MockEngine { _ ->
          throw IllegalStateException("Simulated network error")
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      // We use FindMissing just because it's a simple endpoint.
      // What we really care about testing is the response handling
      // that is common to all our endpoints
      val request =
        FindMissingRequest.newBuilder().apply {
          model = "test-model"
          addAllMemObjectNames(listOf("blob1", "blob2"))
        }.build()

      // Using fully qualified import because it conflicts with Intellij's default assertThrows
      val exception =
        org.junit.Assert.assertThrows(IllegalStateException::class.java) {
          runBlocking { api.findMissing(request) }
        }

      assertTrue(exception.message?.contains("Failed to make network call to find-missing") == true)
      assertTrue(exception.cause?.message == "Simulated network error")
    }

  private fun createGson(): Gson {
    val enumSerializer =
      object : JsonSerializer<NumericEnum> {
        override fun serialize(
          src: NumericEnum,
          typeOfSrc: Type,
          context: JsonSerializationContext,
        ): JsonElement {
          return JsonPrimitive(src.getValue())
        }
      }

    return GsonBuilder()
      .setFieldNamingPolicy(FieldNamingPolicy.LOWER_CASE_WITH_UNDERSCORES)
      .registerTypeHierarchyAdapter(Enum::class.java, enumSerializer)
      .create()
  }

  @Test
  fun testNoBaseUrl() =
    runTest {
      // Clear the completion URL setting
      AugmentSettings.instance.completionURL = null

      val mockEngine =
        MockEngine { _ ->
          throw IllegalStateException("We shouldn't reach here")
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      val request =
        FindMissingRequest.newBuilder().apply {
          model = "test-model"
          addAllMemObjectNames(listOf("blob1", "blob2"))
        }.build()

      val exception =
        org.junit.Assert.assertThrows(IllegalStateException::class.java) {
          runBlocking { api.findMissing(request) }
        }

      assertTrue(exception.message?.contains("Base URL is not set") == true)
    }

  @Test
  fun testChatStreamHandlesSpacesBeforeJsonPayloads() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath.removePrefix("/")) {
            "chat-stream" -> {
              // Create a response with spaces before JSON payloads (heartbeat mechanism)
              val responseContent =
                buildString {
                  append("   {\"text\": \"Hello\"}\n")
                  append("     {\"text\": \" world\"}\n")
                  append("{\"text\": \"!\"}\n")
                  append("  {\"text\": \" How are you?\"}\n")
                  append("       ") // End abruptly with no newline
                }

              respond(
                content = ByteReadChannel(responseContent.toByteArray()),
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      // Create a ChatRequest object
      val chatRequest =
        ChatRequest().apply {
          message = "test message"
          chatHistory = emptyList()
          model = "test-model"
        }

      // Call the chat method and get the flow
      val (requestId, chatFlow) = api.chat(chatRequest)

      // Collect all results from the flow
      val results = chatFlow.toList()

      // Should capture all JSON lines, including those with leading spaces
      // The streaming logic should parse each JSON line correctly despite leading spaces
      assertTrue("Request ID should not be empty", requestId.isNotEmpty())
      assertEquals("Should have received 4 results", 4, results.size)

      // Verify that each result contains the expected text content
      // This validates that JSON with leading spaces was parsed correctly
      assertTrue("First result should contain 'Hello'", results[0].text.contains("Hello"))
      assertTrue("Second result should contain ' world'", results[1].text.contains(" world"))
      assertTrue("Third result should contain '!'", results[2].text.contains("!"))
      assertTrue("Fourth result should contain ' How are you?'", results[3].text.contains(" How are you?"))
    }

  @Test
  fun testGetSubscriptionInfo_Enterprise() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath.removePrefix("/")) {
            "subscription-info" -> {
              respond(
                content = """{"subscription": {"Enterprise": {}}}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      val result = api.getSubscriptionInfo()
      assertTrue(result.isSuccess)
      val response = result.getOrThrow()
      assertNotNull(response.subscription.enterprise)
      assertNull(null, response.subscription.inactiveSubscription)
      assertNull(response.subscription.activeSubscription)
    }

  @Test
  fun testGetSubscriptionInfo_InactiveSubscription() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath.removePrefix("/")) {
            "subscription-info" -> {
              respond(
                content = """{"subscription": {"InactiveSubscription": {}}}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      val result = api.getSubscriptionInfo()
      assertTrue(result.isSuccess)
      val response = result.getOrThrow()
      assertNull(response.subscription.enterprise)
      assertNotNull(null, response.subscription.inactiveSubscription)
      assertNull(response.subscription.activeSubscription)
    }

  @Test
  fun testGetSubscriptionInfo_ActiveSubscriptionWithEndDateNotDepleted() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath.removePrefix("/")) {
            "subscription-info" -> {
              respond(
                content = """{"subscription": {"ActiveSubscription": { "end_date": "example", "usage_balance_depleted": false }}}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      val result = api.getSubscriptionInfo()
      assertTrue(result.isSuccess)
      val response = result.getOrThrow()
      assertNull(response.subscription.enterprise)
      assertNull(response.subscription.inactiveSubscription)
      assertNotNull(response.subscription.activeSubscription)
      assertEquals("example", response.subscription.activeSubscription.endDate)
      assertEquals(false, response.subscription.activeSubscription.usageBalanceDepleted)
    }

  @Test
  fun testGetSubscriptionInfo_ActiveSubscriptionNoEndDateDepleted() =
    runTest {
      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath.removePrefix("/")) {
            "subscription-info" -> {
              respond(
                content = """{"subscription": {"ActiveSubscription": { "usage_balance_depleted": true }}}""",
                status = HttpStatusCode.OK,
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }

      augmentHelpers().registerMockEngine(mockEngine)

      val api = AugmentAPI.instance
      val result = api.getSubscriptionInfo()
      assertTrue(result.isSuccess)
      val response = result.getOrThrow()
      assertNull(response.subscription.enterprise)
      assertNull(response.subscription.inactiveSubscription)
      assertNotNull(response.subscription.activeSubscription)
      assertNull(response.subscription.activeSubscription.endDate)
      assertEquals(true, response.subscription.activeSubscription.usageBalanceDepleted)
    }
}
