package com.augmentcode.intellij.syncing

import com.augmentcode.api.CheckpointBlobsRequest
import com.augmentcode.intellij.pluginstate.PluginState
import com.augmentcode.intellij.testutils.AugmentHeavyPlatformTestCase
import com.augmentcode.intellij.testutils.GsonUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.utils.IndexUtil.expectedBlobName
import com.google.protobuf.util.JsonFormat
import io.ktor.client.engine.mock.*
import io.ktor.http.*
import public_api.PublicApi
import public_api.PublicApi.FindMissingRequest

/**
 * Base test class for syncing tests that contains common helper methods and setup.
 * When the sync tests are run in a suite, there are often difficult to debug test leakages.
 * To save us time debugging tests, it's easier to separate them into their own file.
 */
abstract class SyncV3TestCase : AugmentHeavyPlatformTestCase() {
  protected val gson = GsonUtil.createApiGson()

  override fun setUp() {
    super.setUp()

    augmentHelpers().forcePluginState(
      PluginState.ENABLED,
      augmentHelpers().createGetModelsResponse(
        PublicApi.GetModelsResponse.FeatureFlags.newBuilder()
          .setIntellijIndexingV3Enabled(true)
          .setBypassLanguageFilter(true) // This just makes it easier to test
          .build(),
      ),
    )
  }

  // NOTE: This response causes only .txt files to be indexed.
  // If you're wondering why your file isn't indexed, this is why.
  protected fun mockModelResponse(askForSyncPermission: Boolean = false) =
    """
    {
        "default_model": "test-model",
        "models": [
            {
                "name": "test-model",
                "suggested_prefix_char_count": 100,
                "suggested_suffix_char_count": 100
            }
        ],
        "languages": [
            {
                "name": "Text",
                "vscode_name": "txt",
                "extensions": [".txt"]
            }
        ],
        "feature_flags": {
            "bypass_language_filter": false,
            "max_upload_size_bytes": 1000000,
            "intellij_ask_for_sync_permission_min_version": ${if (askForSyncPermission) "0.0.0" else "999.999.999"}
        }
    }
    """.trimIndent()

  protected data class MockEngineState(
    var checkpointCallCount: Int = 0,
    var uploadedBlobs: MutableSet<String> = mutableSetOf(),
  )

  protected fun createMockEngine(
    state: MockEngineState = MockEngineState(),
    askForSyncPermission: Boolean = false,
  ): MockEngine =
    MockEngine { request ->
      when (request.url.encodedPath) {
        "/get-models" -> {
          respond(
            content = mockModelResponse(askForSyncPermission = askForSyncPermission),
            status = HttpStatusCode.OK,
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        "/batch-upload" -> {
          val requestBody = request.body.toByteArray().decodeToString()
          val uploadRequest =
            PublicApi.BatchUploadRequest.newBuilder().apply {
              JsonFormat.parser().merge(requestBody, this)
            }.build()
          state.uploadedBlobs.addAll(uploadRequest.blobsList.map { expectedBlobName(it.path, it.content) })
          val successfullyUploadedBlobNames = uploadRequest.blobsList.map { expectedBlobName(it.path, it.content) }

          respond(
            content = """{"blob_names": $successfullyUploadedBlobNames}""",
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        "/find-missing" -> {
          val requestBody = request.body.toByteArray().decodeToString()
          val findRequest =
            FindMissingRequest.newBuilder().apply {
              JsonFormat.parser().merge(requestBody, this)
            }.build()
          val unknownBlobs = findRequest.memObjectNamesList.subtract(state.uploadedBlobs)

          respond(
            content = """{"unknown_memory_names":${gson.toJson(unknownBlobs)},"nonindexed_blob_names":[]}""",
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        "/checkpoint-blobs" -> {
          val requestBody = request.body.toByteArray().decodeToString()
          // Check it doesn't throw an exception
          gson.fromJson(requestBody, CheckpointBlobsRequest::class.java)

          state.checkpointCallCount++
          respond(
            content = """{"new_checkpoint_id":"checkpoint-${state.checkpointCallCount}"}""",
            headers = headersOf(HttpHeaders.ContentType, "application/json"),
          )
        }
        else -> error("Unhandled ${request.url.encodedPath}")
      }
    }

  protected fun waitForRequests(
    mockEngine: MockEngine,
    path: String,
    expectedCount: Int,
    timeoutMs: Long = 10000,
    pollIntervalMs: Long = 250,
  ) {
    waitForAssertion({
      assertTrue(mockEngine.requestHistory.count { it.url.encodedPath == path } >= expectedCount)
    }, timeoutMs = timeoutMs, pollIntervalMs = pollIntervalMs)
  }

  // Helper function to join paths consistently
  protected fun joinPath(
    rootPath: String,
    relativePath: String,
  ): String {
    return "$rootPath/$relativePath"
  }

  protected fun String.normalizeLineEndings(): String {
    return replace("\r\n", "\n")
  }
}
