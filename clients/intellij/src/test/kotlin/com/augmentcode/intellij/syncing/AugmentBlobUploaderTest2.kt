package com.augmentcode.intellij.syncing

import com.augmentcode.intellij.index.ignore.PathFilterService
import com.augmentcode.intellij.settings.AugmentSettings
import com.augmentcode.intellij.testutils.AugmentBasePlatformTestCase
import com.augmentcode.intellij.testutils.HttpUtil
import com.augmentcode.intellij.testutils.waitForAssertion
import com.augmentcode.intellij.utils.IndexUtil.expectedBlobName
import com.google.protobuf.util.JsonFormat
import com.intellij.testFramework.LightVirtualFile
import com.intellij.testFramework.registerOrReplaceServiceInstance
import io.ktor.client.engine.mock.MockEngine
import io.ktor.client.engine.mock.respond
import io.ktor.client.engine.mock.toByteArray
import io.ktor.http.HttpHeaders
import io.ktor.http.headersOf
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.JUnit4
import public_api.PublicApi.BatchUploadRequest
import public_api.PublicApi.FindMissingRequest

/**
 * There were some test leakages that I don't care to debug.
 * So I'm copying the tests into a new file.
 */
@RunWith(JUnit4::class)
class AugmentBlobUploaderTest2 : AugmentBasePlatformTestCase() {
  override fun setUp() {
    super.setUp()
    AugmentSettings.instance.apiToken = "test-token"
    AugmentSettings.instance.completionURL = "http://test-server"
  }

  @Test
  fun testUploadBlobIgnoreAcceptIgnore() =
    runBlocking {
      // scenario: Three blobs are enqueued for upload. The one is accepted based on .gitignore/.augmentignore
      // filtering. We expect to see that find-missing and batch-upload are called with only the accepted blob.

      // Set up mock path filter that only accepts specific blob
      val mockPathFilterService = mockk<PathFilterService>(relaxed = true)
      coEvery { mockPathFilterService.isAccepted(any<String>(), any<String>()) } answers {
        firstArg<String>() == "/src" && secondArg<String>() == "accepted.txt"
      }
      project.registerOrReplaceServiceInstance(
        PathFilterService::class.java,
        mockPathFilterService,
        testRootDisposable,
      )
      augmentHelpers().unregisterServiceIfNeeded(AugmentBlobUploaderManager::class.java)

      // Create and enqueue the three blobs
      val ignored1 =
        UploadRequest(
          rootPath = "/src",
          relPath = "ignored1.txt",
          expectedBlobName = expectedBlobName("/src/ignored1.txt", "ignored1 content"),
          fileReference = LightVirtualFile("ignored1.txt", "ignored1 content"),
        )
      val accepted =
        UploadRequest(
          rootPath = "/src",
          relPath = "accepted.txt",
          expectedBlobName = expectedBlobName("/src/accepted.txt", "accepted content"),
          fileReference = LightVirtualFile("accepted.txt", "accepted content"),
        )
      val ignored2 =
        UploadRequest(
          rootPath = "/src",
          relPath = "ignored2.txt",
          expectedBlobName = expectedBlobName("/src/ignored2.txt", "ignored2 content"),
          fileReference = LightVirtualFile("ignored2.txt", "ignored2 content"),
        )

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/find-missing" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val findRequest =
                FindMissingRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()

              // Verify only the accepted blob is in the request
              assertEquals(1, findRequest.memObjectNamesList.size)
              assertEquals(accepted.expectedBlobName, findRequest.memObjectNamesList.first())
              respond(
                content = """{"unknown_memory_names":["${accepted.expectedBlobName}"],"nonindexed_blob_names":[]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()

              // Verify only the accepted blob is being uploaded
              assertEquals(1, uploadRequest.blobsList.size)
              assertEquals("accepted.txt", uploadRequest.blobsList[0].path)
              respond(
                content = """{"blob_names":["${uploadRequest.blobsList[0].let { expectedBlobName(it.path, it.content) } }"]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val blobUploader = AugmentBlobUploaderManager.getInstance(project)
      blobUploader.startUploadLoop()

      runBlocking {
        blobUploader.enqueue(ignored1)
        blobUploader.enqueue(accepted)
        blobUploader.enqueue(ignored2)
      }

      // Verify we only made one find-missing and one batch-upload request
      waitForAssertion({
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      }, timeoutMs = 5000)
    }

  @Test
  fun testUploadBlobMixedAcceptanceAndKnown() =
    runBlocking {
      // Scenario: Three blobs are enqueued for upload. One is rejected by path filtering,
      // one is accepted but already known by the server, and one is both accepted and unknown.
      // We expect only the unknown blob to be uploaded.

      // Set up mock path filter that accepts specific blobs
      val mockPathFilterService = mockk<PathFilterService>(relaxed = true)
      coEvery { mockPathFilterService.isAccepted(any<String>(), any<String>()) } answers {
        firstArg<String>() == "/src" && secondArg<String>() in listOf("accepted.txt", "unknown.txt")
      }
      project.registerOrReplaceServiceInstance(
        PathFilterService::class.java,
        mockPathFilterService,
        testRootDisposable,
      )
      augmentHelpers().unregisterServiceIfNeeded(AugmentBlobUploaderManager::class.java)

      // Create the three test blobs
      val ignored =
        UploadRequest(
          rootPath = "/src",
          relPath = "ignored.txt",
          expectedBlobName = expectedBlobName("/src/ignored.txt", "ignored content"),
          fileReference = LightVirtualFile("ignored.txt", "ignored content"),
        )
      val accepted =
        UploadRequest(
          rootPath = "/src",
          relPath = "accepted.txt",
          expectedBlobName = expectedBlobName("/src/accepted.txt", "accepted content"),
          fileReference = LightVirtualFile("accepted.txt", "accepted content"),
        )
      val unknown =
        UploadRequest(
          rootPath = "/src",
          relPath = "unknown.txt",
          expectedBlobName = expectedBlobName("/src/unknown.txt", "unknown content"),
          fileReference = LightVirtualFile("unknown.txt", "unknown content"),
        )

      val mockEngine =
        MockEngine { request ->
          when (request.url.encodedPath) {
            "/get-models" -> HttpUtil.respondGetModels(this)
            "/find-missing" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val findRequest =
                FindMissingRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()

              // Verify both accepted and unknown are in the request
              assertEquals(2, findRequest.memObjectNamesList.size)
              assertTrue(findRequest.memObjectNamesList.contains(accepted.expectedBlobName))
              assertTrue(findRequest.memObjectNamesList.contains(unknown.expectedBlobName))

              respond(
                content = """{"unknown_memory_names":["${unknown.expectedBlobName}"],"nonindexed_blob_names":[]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            "/batch-upload" -> {
              val requestBody = request.body.toByteArray().decodeToString()
              val uploadRequest =
                BatchUploadRequest.newBuilder().apply {
                  JsonFormat.parser().merge(requestBody, this)
                }.build()

              // Verify only the unknown blob is being uploaded
              assertEquals(1, uploadRequest.blobsList.size)
              assertEquals("unknown.txt", uploadRequest.blobsList[0].path)

              respond(
                content = """{"blob_names":["${uploadRequest.blobsList[0].let { expectedBlobName(it.path, it.content) }}"]}""",
                headers = headersOf(HttpHeaders.ContentType, "application/json"),
              )
            }
            else -> error("Unexpected request to ${request.url.encodedPath}")
          }
        }
      augmentHelpers().registerMockEngine(mockEngine)

      val blobUploader = AugmentBlobUploaderManager.getInstance(project)
      blobUploader.startUploadLoop()

      runBlocking {
        blobUploader.enqueue(ignored)
        blobUploader.enqueue(accepted)
        blobUploader.enqueue(unknown)
      }

      // Verify we only made one find-missing and one batch-upload request
      waitForAssertion({
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/find-missing" })
        assertEquals(1, mockEngine.requestHistory.count { it.url.encodedPath == "/batch-upload" })
      }, timeoutMs = 5000)
    }
}
