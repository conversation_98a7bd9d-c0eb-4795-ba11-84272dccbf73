// list of all engineers
//
// Please add yourself to this list in alphabetical order to get access to services
// username: Use your primary augmentcode.com email address prefix
// gcp_access: 'full' if GCP dev VM access is required, null otherwise
// piiAccess: Can be 'masked', 'general', or 'fraud'. If 'masked', the user will see a hash of PII
//            columns instead of real values and will see null for fraud columns. If 'general', the
//            user will see real values for PII columns and null for fraud columns. If 'fraud', the
//            user will see real values for both PII and fraud columns.
[
  {
    fullname: '<PERSON><PERSON><PERSON>',
    username: 'aarash',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: '<PERSON>',
    username: 'abe',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: '<PERSON>',
    username: 'adam',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: '<PERSON>',
    username: 'aj',
    gcp_access: null,
    github: 'AugmentedA<PERSON>',
    piiAccess: 'masked',
  },
  {
    fullname: 'Akshay Utture',
    username: 'akshay',
    gcp_access: 'full',
    github: 'akshayutture-augment',
    piiAccess: 'masked',
  },
  {
    fullname: 'Alex Ding',
    username: 'alex',
    gcp_access: 'full',
    github: 'ding-alex',
    piiAccess: 'masked',
  },
  {
    fullname: 'Alyah Sablan',
    username: 'alyah',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Amanda Seiglock',
    username: 'amanda',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Andre Chang',
    username: 'andre',
    gcp_access: 'full',
    github: 'a2chang',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Andrew Johnson',
    username: 'andrew',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Anshuman Pandey',
    username: 'anshuman',
    gcp_access: null,
    github: 'anshuman-augment',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Anton Lunev',
    username: 'anton',
    gcp_access: 'full',
    github: 'anton-lunev',
    piiAccess: 'masked',
  },
  {
    fullname: 'Arun Chaganty',
    username: 'arun',
    gcp_access: 'full',
    github: 'arunchaganty',
    piiAccess: 'masked',
  },
  {
    fullname: 'Aswin Karumbunathan',
    username: 'aswin',
    gcp_access: 'full',
    github: 'aswink',
    piiAccess: 'masked',
  },
  {
    fullname: 'Ben Perlmutter',
    username: 'benperlmutter',
    gcp_access: 'full',
    github: 'benperlmutter',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Bin Gao',
    username: 'bin',
    gcp_access: 'full',
    github: 'gaobin415',
    piiAccess: 'masked',
  },
  {
    fullname: 'Bo Nielson',
    username: 'bo',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Brandon Davis',
    username: 'brandon',
    gcp_access: 'full',
    github: 'bddavis',
    // Full PII access needed for agents UXR onboarding.
    piiAccess: 'general',
  },
  {
    fullname: 'Brennan McAdams',
    username: 'brennan',
    gcp_access: null,
    github: 'brennanmcadams',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Cameron Custer',
    username: 'cam',
    gcp_access: 'full',
    github: 'cameroncuster',
    // Full PII access needed for managing customers across external services (Stripe)
    piiAccess: 'general',
  },
  {
    fullname: 'Carl Case',
    username: 'carl',
    gcp_access: 'full',
    github: 'cbcase',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chaitanya Manchikanti',
    username: 'chaitanya',
    gcp_access: 'full',
    github: 'chaitu65c',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chetan Keshav',
    username: 'chetan',
    gcp_access: 'full',
    github: 'chetan15k',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chris Kelly',
    username: 'chris',
    gcp_access: 'full',
    github: 'amateurhuman',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chris Campana',
    username: 'chris.campana',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Claire Garcia',
    username: 'claire',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Dave Clay',
    username: 'clay',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Constantine (Costa) Sapuntzakis',
    username: 'costa',
    gcp_access: 'external',
    github: 'csapuntz',
    // Member of the fraud team.
    piiAccess: 'fraud',
    // Give extra k8s and GCP permissions in dev clusters
    glassbreakerDev: true,
    // Give extra k8s and GCP permissions in prod clusters
    glassbreakerProd: true,
  },
  {
    fullname: 'Daniel Ross',
    username: 'daniel',
    gcp_access: 'full',
    github: 'dross15',
    piiAccess: 'masked',
  },
  {
    fullname: 'David Maskasky',
    username: 'david',
    gcp_access: 'full',
    github: 'dmaskasky',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'David Stephenson',
    username: 'des',
    gcp_access: 'full',
    github: 'D-E-Stephenson',
    // Member of the fraud team
    piiAccess: 'fraud',
  },
  {
    fullname: 'Devang Jhabakh Jai',
    username: 'devang',
    gcp_access: 'full',
    github: 'devangjhabakh',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Diane Huxley',
    username: 'diane',
    gcp_access: 'full',
    github: 'diehuxx',
    piiAccess: 'masked',
  },
  {
    fullname: 'Scott Dietzen',
    username: 'dietz',
    gcp_access: null,
    github: 'scott-dietzen',
    piiAccess: 'masked',
  },
  {
    fullname: 'Dirk Meister',
    username: 'dirk',
    gcp_access: 'external',
    github: 'dmeister',
    piiAccess: 'masked',
    glassbreakerDev: true,
    glassbreakerProd: true,
  },
  {
    fullname: 'Dmitriy Kharchenko',
    username: 'dmitriy',
    gcp_access: 'full',
    github: 'dmitriykharchenko',
    piiAccess: 'masked',
  },
  {
    fullname: 'Eric Hou',
    username: 'eric',
    gcp_access: 'full',
    github: 'ultraeric',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Evan Driscoll',
    username: 'evan',
    gcp_access: 'external',
    github: 'evan0aug',
    piiAccess: 'masked',
  },
  {
    fullname: 'Evelyn Xie',
    username: 'evelyn',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Fedor Korotkov',
    username: 'fedorkorotkov',
    gcp_access: 'full',
    github: 'fkorotkov',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Frank Nunley',
    username: 'frank',
    gcp_access: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Guy Gur-Ari',
    username: 'guy',
    gcp_access: 'full',
    github: 'guygurari',
    piiAccess: 'masked',
  },
  {
    fullname: 'Hannah Fry',
    username: 'hannah',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Harry Dang',
    username: 'harry',
    gcp_access: 'full',
    github: 'Harry-Dang',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Igor Ostrovsky',
    username: 'igor',
    gcp_access: 'full',
    github: 'igor0',
    piiAccess: 'masked',
  },
  {
    fullname: 'Itamar Marom-Yochai',
    username: 'itamar',
    gcp_access: 'full',
    github: 'itamarom',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jacqueline Speiser',
    username: 'jacqueline',
    gcp_access: 'full',
    github: 'jspeiser',
    // PII access needed for validating shredder deletions
    piiAccess: 'general',
    glassbreakerDev: true,
    glassbreakerProd: true,
  },
  {
    fullname: 'Jared Williams',
    username: 'jared',
    gcp_access: 'full',
    github: 'jareddvw',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jason Simard',
    username: 'jason',
    gcp_access: null,
    github: 'jaysym-aug',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Jeffrey Shen',
    username: 'jeff',
    gcp_access: 'full',
    github: 'jeffdshen',
    piiAccess: 'masked',
  },
  {
    fullname: 'Justin Hu',
    username: 'jhu',
    gcp_access: 'full',
    github: 'justinhu-shv',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jiayi Wei',
    username: 'jiayi',
    gcp_access: 'full',
    github: 'MrVPlusOne',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Joel Galenson',
    username: 'joel',
    gcp_access: 'full',
    github: 'jgalenson',
    piiAccess: 'masked',
  },
  {
    fullname: 'Jon Engler',
    username: 'jon',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Jon McLachlan',
    username: 'jonmclachlan',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Julie Bey',
    username: 'julie',
    gcp_access: null,
    github: 'juliebey',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Justin Spears',
    username: 'justin',
    gcp_access: 'full',
    github: 'jspears',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Justin Xu',
    username: 'justinxu',
    gcp_access: 'full',
    github: 'justinxu421',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Khoa Pham',
    username: 'kpham',
    gcp_access: 'full',
    github: 'kpham-prog',
    piiAccess: 'masked',
  },
  {
    fullname: 'Kye Kim',
    username: 'kye',
    gcp_access: 'full',
    github: 'ghkim101',
    piiAccess: 'masked',
  },
  {
    fullname: 'Lawrence Fu',
    username: 'lawrence',
    gcp_access: 'full',
    github: 'lfu77',
    piiAccess: 'masked',
  },
  {
    fullname: 'Liam Lindner',
    username: 'liam',
    gcp_access: 'full',
    github: 'refactornator',
    piiAccess: 'masked',
  },
  {
    fullname: 'Lior Neumann',
    username: 'lior',
    gcp_access: 'full',
    github: 'liornm',
    piiAccess: 'masked',
  },
  {
    fullname: 'Luke Paulsen',
    username: 'luke',
    gcp_access: 'full',
    github: 'augmentluke',
    piiAccess: 'masked',
  },
  {
    fullname: 'Marc MacIntyre',
    username: 'marcmac',
    gcp_access: 'external',
    github: 'marcmac',
    piiAccess: 'masked',
    glassbreakerDev: true,
  },
  {
    fullname: 'Mark Pariente',
    username: 'markp',
    gcp_access: 'full',
    github: 'mrpdaemon',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Markus Rabe',
    username: 'markus',
    gcp_access: 'full',
    github: 'MarkusRabe',
    piiAccess: 'masked',
  },
  {
    fullname: 'Chris Marty',
    username: 'marty',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt Gaunt-Seo',
    username: 'matt',
    gcp_access: 'full',
    github: 'gauntface',
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt Cannizzaro',
    username: 'matt.cannizzaro',
    gcp_access: null,
    github: 'mcannizz',
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt McClernan',
    username: 'matt.mcclernan',
    gcp_access: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Matt Monaco',
    username: 'mattm',
    gcp_access: 'full',
    github: 'mmonaco',
    piiAccess: 'masked',
  },
  {
    fullname: 'Max Hahn',
    username: 'maxhahn',
    gcp_access: 'full',
    github: 'augment-mhahn',
    piiAccess: 'masked',
  },
  {
    fullname: 'Mayur Nagarsheth',
    username: 'mayur',
    gcp_access: null,
    github: 'mayurnagarsheth1',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Matt Ball',
    username: 'mb',
    gcp_access: null,
    github: 'matt-ball',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Mike Mao',
    username: 'mike',
    gcp_access: 'full',
    github: 'mike-meow',
    piiAccess: 'masked',
  },
  {
    fullname: 'Mirolsav Gavrilov',
    username: 'miroslav',
    gcp_access: 'full',
    github: 'mgavrilov-augmentcode',
    piiAccess: 'masked',
  },
  {
    fullname: 'Matt Legrand',
    username: 'ml',
    gcp_access: 'full',
    github: 'insprd',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
  {
    fullname: 'Molisha Shah',
    username: 'molisha',
    gcp_access: 'full',
    github: 'molishashah',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Guy Mograbi',
    username: 'moogi',
    gcp_access: 'full',
    github: 'moogi-augment',
    // Member of the fraud team
    piiAccess: 'fraud',
  },
  {
    fullname: 'Matt Pauly',
    username: 'mpauly',
    gcp_access: 'full',
    github: 'mtpauly',
    // Full PII access needed for managing remote agents rollout.
    piiAccess: 'general',
  },
  {
    fullname: 'Nathan Rockenbach',
    username: 'nathro',
    gcp_access: 'full',
    github: 'nathro',
    piiAccess: 'masked',
  },
  {
    fullname: 'Navtej Sadhal',
    username: 'navtej',
    gcp_access: 'full',
    github: 'navtej-ac',
    piiAccess: 'masked',
  },
  {
    fullname: 'Neil Vexler',
    username: 'neil',
    gcp_access: 'full',
    github: 'neil-augment',
    piiAccess: 'masked',
  },
  {
    fullname: 'Nikita Sirohi',
    username: 'nikita',
    gcp_access: 'full',
    github: 'nikita-sirohi',
    // Temporary PII access to do review and data deletions
    piiAccess: 'general',
  },
  {
    fullname: 'Oliver Schober',
    username: 'oliver',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Omer Bensaadon',
    username: 'omer',
    gcp_access: null,
    github: 'omerbensaadon',
    piiAccess: 'masked',
  },
  {
    fullname: 'Omendra Singh Rathor',
    username: 'osr',
    gcp_access: 'full',
    github: 'osr-augment',
    piiAccess: 'masked',
  },
  {
    fullname: 'Pranay Agrawal',
    username: 'pranay',
    gcp_access: 'full',
    github: 'pranayagra',
    piiAccess: 'masked',
  },
  {
    fullname: 'Preston Jernegan',
    username: 'preston',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Ran Halprin',
    username: 'ran',
    gcp_access: 'full',
    github: 'ranhalprin',
    piiAccess: 'masked',
  },
  {
    fullname: 'Rich Hankins',
    username: 'rich',
    gcp_access: 'full',
    github: 'richhankins',
    // Member of the fraud team.
    piiAccess: 'fraud',
  },
  {
    fullname: 'Ricky Dinh',
    username: 'ricky',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Robert Kitaoka',
    username: 'rob',
    gcp_access: 'full',
    github: 'rob-aug',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Sachal Jogi',
    username: 'sach',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Sasha Sinkevich',
    username: 'sashasinkevich',
    gcp_access: null,
    github: 'interstellarPotato',
    // General PII access needed for data protection and data deletion validation
    piiAccess: 'general',
  },
  {
    fullname: 'Shaohua Zhou',
    username: 'shaohua',
    gcp_access: 'full',
    github: 'shaohua-augment',
    piiAccess: 'masked',
  },
  {
    fullname: 'Sharath Rao',
    username: 'sharath',
    gcp_access: null,
    github: 'sharath333',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Shawn Xiangcao Liu',
    username: 'shawn',
    gcp_access: 'full',
    github: 'shawn-augmentcode',
    piiAccess: 'masked',
  },
  {
    fullname: 'Siyao Li',
    username: 'siyao',
    gcp_access: 'full',
    github: 'siyaoL1',
    // Full PII access for performing user data deletion
    piiAccess: 'general',
  },
  {
    fullname: 'Sophie Reynolds',
    username: 'sophie',
    gcp_access: 'full',
    github: 'shreynolds',
    piiAccess: 'masked',
  },
  {
    fullname: 'Sudeeksha Murari',
    username: 'sudeeksha',
    gcp_access: 'full',
    github: 'sudeeksha-murari',
    // Member of the fraud team
    piiAccess: 'fraud',
  },
  {
    fullname: 'Surbhi Jain',
    username: 'surbhi',
    gcp_access: 'full',
    github: 'surbhiijain',
    // Full PII access needed for managing customers across external services (Stripe)
    piiAccess: 'general',
  },
  {
    fullname: 'Syl Giuliani',
    username: 'syl',
    gcp_access: null,
    github: 'sylg',
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Tamuz Hod',
    username: 'tamuz',
    gcp_access: 'full',
    github: 'TamuzHod',
    piiAccess: 'masked',
  },
  {
    fullname: 'Tenzin Low',
    username: 'tenzin',
    gcp_access: 'full',
    github: 'tenzinhl',
    piiAccess: 'masked',
  },
  {
    fullname: 'Teva Tafiti',
    username: 'teva',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Todd Hausman',
    username: 'todd',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Tongfei Chen',
    username: 'tongfei',
    gcp_access: 'full',
    github: 'ctongfei',
    piiAccess: 'masked',
  },
  {
    fullname: 'Tony Chu',
    username: 'tony',
    gcp_access: 'full',
    github: 'tonyhschu',
    piiAccess: 'masked',
  },
  {
    fullname: 'Tulga Tsogtgerel',
    username: 'tulga',
    gcp_access: 'full',
    github: 'tulga-bytes',
    piiAccess: 'masked',
  },
  {
    fullname: 'Vaibhav Agrawal',
    username: 'vaibhav',
    gcp_access: 'full',
    github: 'vaibagra',
    piiAccess: 'masked',
  },
  {
    fullname: 'Vinay Perneti',
    username: 'vinay',
    gcp_access: 'full',
    github: 'vperneti',
    piiAccess: 'masked',
  },
  {
    fullname: 'Vincent Wong',
    username: 'vincentwong',
    gcp_access: null,
    github: null,
    piiAccess: 'masked',
  },
  {
    fullname: 'Viktor Passichenko',
    username: 'vpas',
    gcp_access: 'full',
    github: 'vpas',
    piiAccess: 'masked',
  },
  {
    fullname: 'Wyatt Richter',
    username: 'wyatt',
    gcp_access: null,
    github: null,
    // General PII access needed for managing customer relations.
    piiAccess: 'general',
  },
  {
    fullname: 'Zach Dahlgren',
    username: 'zach',
    gcp_access: 'full',
    github: null,
    piiAccess: 'fraud',
  },
  {
    fullname: 'Zheren Dong',
    username: 'zheren',
    gcp_access: 'full',
    github: 'Edwardong',
    piiAccess: 'masked',
  },
  {
    fullname: 'Zhuoran Shen',
    username: 'zhuoran',
    gcp_access: 'full',
    github: 'cmsflash',
    piiAccess: 'masked',
    alternativeGithubEmails: ['<EMAIL>'],
  },
]
